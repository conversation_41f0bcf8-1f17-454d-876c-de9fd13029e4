"""
GS转换工具的核心API模块
提供标准转换和兼容模式转换的统一接口，封装maya_gs_coverter包的功能
"""

# Import built-in modules
import logging
from typing import Callable
from typing import Optional

# Import third-party modules
from blade_client_reporter import get_reporter

# Import local modules
from cgame_avatar_factory.hair_studio.maya_api.utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)

# Import third-party modules
import maya_gs_coverter.compatability_mode.converter as compatability_converter
from maya_gs_coverter.hair import generate_lod
from maya_gs_coverter.hair import process
from maya_gs_coverter.hair import simplify_curve_graph


class GSConvertAPI:
    """
    GS转换工具的核心API类

    提供毛发面片到GS曲线的转换功能，包括：
    - 标准转换：支持逐顶点匹配的高精度转换
    - 兼容模式转换：快速转换模式
    - 曲线简化和LOD生成功能
    """

    def __init__(self):
        """初始化API实例"""
        self.logger = logging.getLogger(__name__)
        self._progress_callback = None

    def set_progress_callback(self, callback: Optional[Callable[[int, int, str], None]]):
        """
        设置进度回调函数

        Args:
            callback: 进度回调函数，接收参数 (current, total, message)
        """
        self._progress_callback = callback

    def _create_progress_wrapper(self):
        """创建进度条包装器，用于适配原有的进度条接口"""
        if not self._progress_callback:
            return None, None

        class ProgressWrapper:
            def __init__(self, callback):
                self.callback = callback
                self.current = 0
                self.total = 100

            def setValue(self, value):
                self.current = value
                self.callback(self.current, self.total, f"已处理 {self.current} / {self.total}")

            def setMaximum(self, maximum):
                self.total = maximum

        class TextWrapper:
            def __init__(self, callback):
                self.callback = callback

            def setText(self, text):
                # 从文本中提取进度信息
                self.callback(0, 0, text)

        progress_bar = ProgressWrapper(self._progress_callback)
        progress_text = TextWrapper(self._progress_callback)

        return progress_bar, progress_text

    def standard_convert(
        self,
        do_vertex_match: bool = False,
        only_create_curve: bool = False,
        progress_bar=None,
        progress_text=None,
    ) -> bool:
        """
        执行标准转换

        Args:
            do_vertex_match: 是否进行逐顶点匹配，提高转换精度
            only_create_curve: 是否仅提取曲线，不生成完整的GS网格
            progress_bar: 进度条控件（可选），如果提供则直接使用，否则使用回调包装器
            progress_text: 进度文本控件（可选），如果提供则直接使用，否则使用回调包装器

        Returns:
            bool: 转换是否成功

        Note:
            此方法完全复制原始 hair_tool_window.py 中 on_process() 的逻辑：
            1. 统计上报
            2. 获取逐顶点匹配选项
            3. 调用 maya_gs_coverter.hair.process()
            4. process() 内部会通过 get_selected_meshes() 获取当前Maya选择

            支持两种进度反馈方式：
            - 直接传入UI控件（与原始UI完全一致）
            - 使用回调函数（通过 set_progress_callback 设置）
        """
        try:
            self.logger.info(f"开始标准转换，逐顶点匹配: {do_vertex_match}")

            # === 以下逻辑完全复制原始 on_process() ===

            # 统计上报：构建
            with get_reporter(app_name="maya_gs_coverter", browser_name="maya2022") as api:
                api.report_count(event_name="build", action="build gs group", tool_name="cgame_avatar_factory")

            # 如果直接传入了进度条控件，使用它们；否则使用回调包装器
            if progress_bar is not None or progress_text is not None:
                # 直接使用传入的UI控件（与原始UI完全一致）
                final_progress_bar = progress_bar
                final_progress_text = progress_text
            else:
                # 使用回调包装器
                final_progress_bar, final_progress_text = self._create_progress_wrapper()

            # 调用功能模块逻辑（与原始UI完全一致）
            process(
                do_vertex_match=do_vertex_match,
                only_create_curve=only_create_curve,
                progress_bar=final_progress_bar,
                progress_text=final_progress_text,
            )

            self.logger.info("标准转换完成")
            return True

        except Exception as e:
            self.logger.error(f"标准转换失败: {e}")
            cmds.warning(f"标准转换失败: {e}")
            return False

    def compatibility_convert(self, only_create_curve: bool = False, progress_bar=None, progress_text=None) -> bool:
        """
        执行兼容模式转换

        Args:
            only_create_curve: 是否仅提取曲线，不生成完整的GS网格
            progress_bar: 进度条控件（可选），如果提供则直接使用，否则使用回调包装器
            progress_text: 进度文本控件（可选），如果提供则直接使用，否则使用回调包装器

        Returns:
            bool: 转换是否成功

        Note:
            此方法完全复制原始 hair_tool_window.py 中 on_compat() 的逻辑：
            1. 调用 compatability_converter.convert_selected_hair_cards_to_gs_cards_batch()
            2. 该函数内部会通过 hair.batch_convert_selected() 获取当前Maya选择

            支持两种进度反馈方式：
            - 直接传入UI控件（与原始UI完全一致）
            - 使用回调函数（通过 set_progress_callback 设置）
        """
        try:
            self.logger.info(f"开始兼容模式转换，only_create_curve={only_create_curve}")

            # === 以下逻辑完全复制原始 on_compat() ===

            # 如果直接传入了进度条控件，使用它们；否则使用回调包装器
            if progress_bar is not None or progress_text is not None:
                # 直接使用传入的UI控件（与原始UI完全一致）
                final_progress_bar = progress_bar
                final_progress_text = progress_text
            else:
                # 使用回调包装器
                final_progress_bar, final_progress_text = self._create_progress_wrapper()

            # 执行兼容模式的批量转换（按标准转换相同的拆分与分组规则）
            # 兼容不同版本的函数签名
            try:
                # 尝试使用新版本的函数签名（包含only_create_curve参数）
                compatability_converter.convert_selected_hair_cards_to_gs_cards_batch(
                    progress_bar=final_progress_bar,
                    progress_text=final_progress_text,
                    only_create_curve=only_create_curve,
                )
            except TypeError as e:
                if "unexpected keyword argument 'only_create_curve'" in str(e):
                    # 如果函数不支持only_create_curve参数，使用旧版本调用
                    self.logger.warning("兼容模式函数不支持only_create_curve参数，使用旧版本调用")
                    if only_create_curve:
                        self.logger.warning("仅提取曲线功能在当前版本的兼容模式下不可用")
                    compatability_converter.convert_selected_hair_cards_to_gs_cards_batch(
                        progress_bar=final_progress_bar,
                        progress_text=final_progress_text,
                    )
                else:
                    # 其他TypeError，重新抛出
                    raise

            self.logger.info("兼容模式转换完成")
            return True

        except Exception as e:
            self.logger.error(f"兼容模式转换失败: {e}")
            cmds.warning(f"兼容模式转换失败: {e}")
            return False

    def simplify_curve(self, graph_type: str = "scale", selected_curves: Optional[list] = None) -> bool:
        """
        简化曲线图形

        Args:
            graph_type: 图形类型，可选 "scale", "twist", "profile"
            selected_curves: 指定要处理的曲线列表，None则使用当前选择

        Returns:
            bool: 简化是否成功
        """
        try:
            self.logger.info(f"开始简化曲线图形，类型: {graph_type}")

            # 如果指定了特定曲线，需要先选择它们
            if selected_curves:
                cmds.select(selected_curves, replace=True)

            # 调用原有的简化逻辑
            simplify_curve_graph(graph_type)

            self.logger.info("曲线图形简化完成")
            return True

        except Exception as e:
            self.logger.error(f"曲线图形简化失败: {e}")
            cmds.warning(f"曲线图形简化失败: {e}")
            return False

    def generate_lod_curves(self, percent: int = 80, selected_curves: Optional[list] = None) -> bool:
        """
        生成LOD曲线

        Args:
            percent: 减面百分比 (0-200)
            selected_curves: 指定要处理的曲线列表，None则使用当前选择

        Returns:
            bool: LOD生成是否成功
        """
        try:
            self.logger.info(f"开始生成LOD曲线，减面百分比: {percent}%")

            # 如果指定了特定曲线，需要先选择它们
            if selected_curves:
                cmds.select(selected_curves, replace=True)
            else:
                selected_curves = cmds.ls(sl=True)

            if not selected_curves:
                cmds.warning("未选择任何对象！")
                return False

            # 查找可处理的曲线
            curves_to_process = set()
            for obj in selected_curves:
                if cmds.objectType(obj, isType="transform"):
                    children = cmds.listRelatives(obj, children=True, fullPath=True) or []
                    for child in children:
                        if cmds.objectType(child, isType="nurbsCurve"):
                            parent = cmds.listRelatives(child, parent=True, fullPath=True)[0]
                            curves_to_process.add(parent)
                        elif cmds.objectType(child, isType="transform"):
                            shape = cmds.listRelatives(child, shapes=True, fullPath=True)
                            if shape and cmds.objectType(shape[0], isType="nurbsCurve"):
                                curves_to_process.add(child)
                    shape = cmds.listRelatives(obj, shapes=True, fullPath=True)
                    if shape and cmds.objectType(shape[0], isType="nurbsCurve"):
                        curves_to_process.add(obj)
                    elif cmds.objectType(obj, isType="nurbsCurve"):
                        parent = cmds.listRelatives(obj, parent=True, fullPath=True)[0]
                        curves_to_process.add(parent)

            if not curves_to_process:
                cmds.warning("未找到可处理的曲线！")
                return False

            # 生成LOD
            for curve in curves_to_process:
                generate_lod(curve, percent)

            self.logger.info(f"LOD曲线生成完成，处理了 {len(curves_to_process)} 个曲线")
            return True

        except Exception as e:
            self.logger.error(f"LOD曲线生成失败: {e}")
            cmds.warning(f"LOD曲线生成失败: {e}")
            return False


# 便捷函数，提供简单的函数式接口
def convert_hair_standard(
    do_vertex_match: bool = False,
    only_create_curve: bool = False,
    progress_callback: Optional[Callable[[int, int, str], None]] = None,
) -> bool:
    """
    标准毛发转换的便捷函数

    Args:
        do_vertex_match: 是否进行逐顶点匹配
        only_create_curve: 是否仅提取曲线，不生成完整的GS网格
        progress_callback: 进度回调函数

    Returns:
        bool: 转换是否成功

    Note:
        依赖Maya当前选择的网格对象，不接受mesh参数
    """
    api = GSConvertAPI()
    if progress_callback:
        api.set_progress_callback(progress_callback)
    return api.standard_convert(do_vertex_match=do_vertex_match, only_create_curve=only_create_curve)


def convert_hair_compatibility(
    only_create_curve: bool = False,
    progress_callback: Optional[Callable[[int, int, str], None]] = None,
) -> bool:
    """
    兼容模式毛发转换的便捷函数

    Args:
        only_create_curve: 是否仅提取曲线，不生成完整的GS网格
        progress_callback: 进度回调函数

    Returns:
        bool: 转换是否成功

    Note:
        依赖Maya当前选择的网格对象，不接受mesh参数
    """
    api = GSConvertAPI()
    if progress_callback:
        api.set_progress_callback(progress_callback)
    return api.compatibility_convert(only_create_curve=only_create_curve)


def simplify_curves(
    graph_type: str = "scale",
    selected_curves: Optional[list] = None,
) -> bool:
    """
    简化曲线的便捷函数

    Args:
        graph_type: 图形类型
        selected_curves: 指定要处理的曲线列表

    Returns:
        bool: 简化是否成功
    """
    api = GSConvertAPI()
    return api.simplify_curve(graph_type, selected_curves)


def generate_lod(
    percent: int = 80,
    selected_curves: Optional[list] = None,
) -> bool:
    """
    生成LOD的便捷函数

    Args:
        percent: 减面百分比
        selected_curves: 指定要处理的曲线列表

    Returns:
        bool: LOD生成是否成功
    """
    api = GSConvertAPI()
    return api.generate_lod_curves(percent, selected_curves)
