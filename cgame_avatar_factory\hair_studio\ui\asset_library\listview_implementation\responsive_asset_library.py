"""Responsive Asset Library using QListView implementation.

This module provides the main ResponsiveAssetLibrary component that integrates
the AssetListModel, AssetGridView, and AssetItemDelegate to create a complete
asset library widget that replaces the original QScrollArea + QGridLayout implementation.
"""

# Import built-in modules
import logging

# Import third-party modules
# Use MTabWidget for consistency with main tabs
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>
from dayu_widgets import MLineEdit
from dayu_widgets import MTabWidget
from dayu_widgets import MToolButton
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import BOTTOM_AREA_SPACING
from cgame_avatar_factory.hair_studio.constants import FORM_SEPARATOR_STYLE
from cgame_avatar_factory.hair_studio.constants import ICON_SEARCH_LINE
from cgame_avatar_factory.hair_studio.constants import ICON_SETTINGS_LINE
from cgame_avatar_factory.hair_studio.constants import LAYOUT_MARGIN_ZERO
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_MEDIUM
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_SMALL
from cgame_avatar_factory.hair_studio.constants import SLIDER_AREA_HEIGHT
from cgame_avatar_factory.hair_studio.constants import SLIDER_AREA_MARGIN
from cgame_avatar_factory.hair_studio.constants import SLIDER_AREA_SLIDER_WIDTH
from cgame_avatar_factory.hair_studio.constants import SUBTITLE_STYLE
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_SETTINGS
from cgame_avatar_factory.hair_studio.constants import WHEEL_ZOOM_DEFAULT_SCALE
from cgame_avatar_factory.hair_studio.constants import WHEEL_ZOOM_MAX_SCALE
from cgame_avatar_factory.hair_studio.constants import WHEEL_ZOOM_MIN_SCALE
from cgame_avatar_factory.hair_studio.constants import (
    BORDER_RADIUS_MEDIUM,
)  # Bottom area constants for unified height management
from cgame_avatar_factory.hair_studio.constants import BOTTOM_AREA_PADDING_HORIZONTAL
from cgame_avatar_factory.hair_studio.constants import SLIDER_AREA_SLIDER_HEIGHT
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_HAIR_ASSET_LIBRARY
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_SEARCH_PLACEHOLDER
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_grid_view import AssetGridView
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_item_delegate import (
    AssetItemDelegate,
)
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import AssetListModel
from cgame_avatar_factory.hair_studio.utils.icon_utils import set_button_icon_with_fallback


class ResponsiveAssetLibrary(QtWidgets.QWidget):
    """Main asset library component using QListView implementation.

    This widget provides a complete asset library interface that integrates:
    - AssetListModel for data management
    - AssetGridView for display
    - AssetItemDelegate for rendering

    Features:
    - Responsive grid layout that automatically adjusts to window size
    - Consistent spacing that doesn't change with window height
    - Proper width matching between container and content
    - Search and filter functionality
    - Scale/zoom controls
    - Drag and drop support
    - Selection and interaction handling

    Signals:
        assetSelected(dict): Emitted when an asset is selected
        assetDragStarted(dict): Emitted when asset drag operation starts
        assetDoubleClicked(dict): Emitted when an asset is double-clicked
    """

    # Signals for compatibility with existing BaseHairTab interface
    assetSelected = QtCore.Signal(dict)
    assetDragStarted = QtCore.Signal(dict)
    assetDoubleClicked = QtCore.Signal(dict)

    def __init__(self, hair_type=None, hair_manager=None, parent=None):
        """Initialize the responsive asset library.

        Args:
            hair_type (str, optional): Type of hair assets to display
            hair_manager (object, optional): Hair manager instance
            parent (QWidget, optional): Parent widget
        """
        super().__init__(parent)

        # Store initialization parameters
        self._hair_type = hair_type
        self._hair_manager = hair_manager

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize configuration
        self._config = AssetLibConfig()

        # Compatibility attributes for BaseHairTab
        self.hair_type = hair_type
        self.manager = hair_manager

        # Initialize components
        self._model = None
        self._view = None
        self._delegate = None

        # UI components
        self._title_label = None
        self._search_box = None
        self._scale_slider = None
        self._scale_label = None

        # Current state tracking
        self._current_assets = []
        self._current_filter_text = ""
        self._current_sub_type_filter = None  # Current sub-type filter state
        self._pending_sub_type_filter = None  # to handle tab changes before model is ready

        # Sub-type tab management (for card type only)
        self._tab_to_subtype = {}  # Maps tab index to sub-type string

        # Setup UI
        self._setup_ui()
        self._setup_components()
        self._setup_connections()

        self._logger.debug(
            f"ResponsiveAssetLibrary initialized for hair_type: {hair_type}",
        )

    def _setup_ui(self):
        """Setup the user interface layout."""
        # Main layout - match original AssetLibrary margins
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
        )
        main_layout.setSpacing(LAYOUT_SPACING_MEDIUM)

        # Header section
        header_widget = self._create_header_section()
        main_layout.addWidget(header_widget)

        # Content area with sub-type tabs (main content)
        content_widget = self._create_content_area_with_tabs()
        main_layout.addWidget(content_widget, 1)  # Stretch factor 1 to take remaining space

        # Footer section (controls)
        footer_widget = self._create_footer_section()
        main_layout.addWidget(footer_widget)

        # Set widget properties
        self.setMinimumSize(300, 400)

    def _create_content_area_with_tabs(self):
        """Creates the main content area with optional sub-type tabs and asset grid view.

        For card hair type, creates a vertical layout with sub-type tabs on the top
        and the asset grid view below. For other hair types, only shows the
        asset grid view at full width.

        Returns:
            QWidget: The content area widget containing tabs and/or grid view.
        """
        content_widget = QtWidgets.QWidget()
        content_layout = QtWidgets.QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(LAYOUT_SPACING_MEDIUM)

        # Add sub-type tabs on the top (based on configuration)
        if self._config.should_show_sub_tabs(self._hair_type):
            self._tab_widget = self._create_sub_type_tabs()
            content_layout.addWidget(self._tab_widget)

        # Add asset grid view below the tabs (main content area)
        self._view = AssetGridView(parent=self)
        content_layout.addWidget(self._view, 1)  # Stretch factor 1 for main content

        return content_widget

    def _create_sub_type_tabs(self):
        """Create sub-type tabs for card hair type with dayu_theme styling.

        Creates a horizontal tab widget that matches the main Hair Studio tabs appearance,
        using dayu_widgets styling and theme colors for consistency.

        Returns:
            QTabWidget: Horizontal tab widget for sub-types with dayu_theme styling
        """
        tab_widget = MTabWidget()
        tab_widget.setTabPosition(QtWidgets.QTabWidget.North)  # Top horizontal tabs

        # Set fixed height for horizontal tabs
        tab_widget.setFixedHeight(40)  # Fixed height for horizontal tab bar

        # Initialize tab index to sub-type mapping
        self._tab_to_subtype = {}

        # Create tabs for each sub-type defined in configuration
        tab_keys = self._config.get_tab_keys()
        for i, sub_type in enumerate(tab_keys):
            tab_content = QtWidgets.QWidget()  # Empty widget, only tab label is needed
            display_text = self._config.get_tab_display_text(sub_type)
            tab_widget.addTab(tab_content, display_text)
            self._tab_to_subtype[i] = sub_type

        # Connect tab selection change signal to handler
        tab_widget.currentChanged.connect(self._on_sub_type_tab_changed)

        # Set default selection based on configuration
        default_tab_key = self._config.get_default_tab_key()
        default_index = self._config.get_default_tab_index()

        if default_tab_key and default_index >= 0:
            tab_widget.setCurrentIndex(default_index)
            # Apply initial filter for default sub-type
            self.setSubTypeFilter(default_tab_key)
        elif self._config.get_tab_count() > 0:
            # Fallback to first available tab
            first_sub_type = self._config.get_tab_keys()[0]
            tab_widget.setCurrentIndex(0)
            self.setSubTypeFilter(first_sub_type)

        self._logger.debug(f"Created sub-type tabs with {self._config.get_tab_count()} tabs")
        return tab_widget

    def _on_sub_type_tab_changed(self, index):
        """Handles sub-type tab selection changes.

        When a user clicks on a different sub-type tab (eyebrow, hair, beard),
        this method updates the asset filter to show only assets of the selected type.

        Args:
            index (int): The index of the newly selected tab.
        """
        if hasattr(self, "_tab_to_subtype") and index in self._tab_to_subtype:
            sub_type = self._tab_to_subtype[index]
            self.setSubTypeFilter(sub_type)
            self._logger.debug(f"Sub-type tab changed to: {sub_type}")
        else:
            self._logger.warning(f"Invalid tab index received: {index}")

    def _create_header_section(self):
        """Create the header section with title, settings button and search.

        Returns:
            QWidget: Header widget
        """
        header_widget = QtWidgets.QWidget()
        header_main_layout = QtWidgets.QVBoxLayout(header_widget)
        header_main_layout.setContentsMargins(0, 0, 0, 0)
        header_main_layout.setSpacing(LAYOUT_SPACING_MEDIUM)

        # Header layout with title and settings button (matching original)
        header_layout = QtWidgets.QHBoxLayout()
        header_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )
        header_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Add stretch to center the title
        header_layout.addStretch()

        # Title (subtitle style) - ensure consistent height with component list
        title = MLabel(UI_TEXT_HAIR_ASSET_LIBRARY)
        title.setProperty("h2", True)
        # Apply subtitle styling for asset library header
        title.setStyleSheet(SUBTITLE_STYLE)
        # Center align the title
        title.setAlignment(QtCore.Qt.AlignCenter)
        # Force consistent height to match component list header
        title.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Fixed,
        )
        header_layout.addWidget(title)

        # Add stretch to center the title and push settings button to the right
        header_layout.addStretch()

        # Settings button (gear icon)
        self.settings_btn = MToolButton()
        set_button_icon_with_fallback(self.settings_btn, ICON_SETTINGS_LINE)
        self.settings_btn.setToolTip(UI_TEXT_SETTINGS)
        self.settings_btn.clicked.connect(self._on_settings_clicked)
        header_layout.addWidget(self.settings_btn)

        header_main_layout.addLayout(header_layout)

        # Add separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        header_main_layout.addWidget(separator)

        # Search layout (matching original)
        search_layout = QtWidgets.QHBoxLayout()
        search_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )
        search_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Search icon
        search_icon = MToolButton()
        set_button_icon_with_fallback(search_icon, ICON_SEARCH_LINE)
        search_icon.setEnabled(False)  # Just for display
        search_layout.addWidget(search_icon)

        # Search input
        self._search_box = MLineEdit()
        self._search_box.setPlaceholderText(UI_TEXT_SEARCH_PLACEHOLDER)
        self._search_box.textChanged.connect(self._on_search_text_changed)
        search_layout.addWidget(self._search_box)

        header_main_layout.addLayout(search_layout)

        return header_widget

    def _create_footer_section(self):
        """Create the footer section with zoom slider control (matching original).

        Returns:
            QWidget: Footer widget
        """
        # Add separator before footer
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        separator.setStyleSheet(FORM_SEPARATOR_STYLE)

        # Create horizontal layout for slider positioning with unified height
        slider_layout = QtWidgets.QHBoxLayout()
        slider_layout.setContentsMargins(
            BOTTOM_AREA_PADDING_HORIZONTAL,
            SLIDER_AREA_MARGIN,
            BOTTOM_AREA_PADDING_HORIZONTAL,
            SLIDER_AREA_MARGIN,
        )
        slider_layout.setSpacing(BOTTOM_AREA_SPACING)

        # Add stretch to push slider to the right
        slider_layout.addStretch()

        # Create zoom slider with consistent sizing
        self._scale_slider = QtWidgets.QSlider(QtCore.Qt.Horizontal)
        self._scale_slider.setMinimum(int(WHEEL_ZOOM_MIN_SCALE * 100))  # 50
        self._scale_slider.setMaximum(int(WHEEL_ZOOM_MAX_SCALE * 100))  # 200
        self._scale_slider.setValue(int(WHEEL_ZOOM_DEFAULT_SCALE * 100))  # 100
        # Use consistent slider dimensions from constants
        self._scale_slider.setFixedWidth(SLIDER_AREA_SLIDER_WIDTH)
        self._scale_slider.setFixedHeight(SLIDER_AREA_SLIDER_HEIGHT)
        self._scale_slider.setToolTip("调整资产项目大小")

        # Connect slider signal
        self._scale_slider.valueChanged.connect(self._on_scale_changed)

        # Add slider to layout
        slider_layout.addWidget(self._scale_slider)

        # Create container widget for slider layout with fixed height
        slider_container = QtWidgets.QWidget()
        slider_container.setFixedHeight(SLIDER_AREA_HEIGHT)
        slider_container.setLayout(slider_layout)

        # Create footer widget that contains both separator and slider
        footer_widget = QtWidgets.QWidget()
        footer_main_layout = QtWidgets.QVBoxLayout(footer_widget)
        footer_main_layout.setContentsMargins(0, 0, 0, 0)
        footer_main_layout.setSpacing(LAYOUT_SPACING_SMALL)

        footer_main_layout.addWidget(separator)
        footer_main_layout.addWidget(slider_container)

        return footer_widget

    def _on_settings_clicked(self):
        """Handle settings button click."""
        # TODO: Implement settings dialog
        self._logger.info("Settings button clicked - not implemented yet")

    def _on_search_text_changed(self, text):
        """Handle search text changes.

        Args:
            text (str): Search text
        """
        try:
            if hasattr(self, "_model") and self._model:
                self._model.setFilterText(text)
                self._logger.debug(f"Search filter applied: '{text}'")
        except Exception as e:
            self._logger.error(f"Error applying search filter: {e}")

    def _setup_components(self):
        """Setup the core components (model, delegate)."""
        # Create model - let it use its own logger
        self._model = AssetListModel()

        # Create delegate - let it use its own logger
        self._delegate = AssetItemDelegate(parent=self._view)

        # Set model and delegate on view
        self._view.setModel(self._model)
        self._view.setItemDelegate(self._delegate)

        # apply pending sub-type filter if any
        if hasattr(self, "_pending_sub_type_filter"):
            self._model.setSubTypeFilter(self._pending_sub_type_filter)
            delattr(self, "_pending_sub_type_filter")

    def _setup_connections(self):
        """Setup signal connections."""
        # Search box
        self._search_box.textChanged.connect(self._on_search_text_changed)

        # Scale slider
        self._scale_slider.valueChanged.connect(self._on_scale_changed)

        # View signals - forward to our signals for compatibility
        self._view.assetSelected.connect(self.assetSelected.emit)
        self._view.assetDragStarted.connect(self.assetDragStarted.emit)
        self._view.assetDoubleClicked.connect(self.assetDoubleClicked.emit)

    def _on_search_text_changed(self, text):
        """Handle search text changes.

        Args:
            text (str): New search text
        """
        self._current_filter_text = text
        if self._model:
            self._model.setFilterText(text)
        self._logger.debug(f"Search filter set to: '{text}'")

    def _on_scale_changed(self, value):
        """Handle scale slider changes.

        Args:
            value (int): Slider value (50-200)
        """
        scale_factor = value / 100.0  # Convert to 0.5-2.0 range

        # Update delegate scale
        if self._delegate:
            self._delegate.setScale(scale_factor)

        # Trigger view update (QListView handles layout automatically)
        if self._view:
            self._view.update()

        self._logger.debug(f"Scale set to {scale_factor}x ({value}%)")

    # Public interface methods for compatibility

    def updateAssets(self, assets):
        """Update the assets displayed in the library.

        Args:
            assets (list): List of asset dictionaries
        """
        self._current_assets = assets or []
        if self._model:
            self._model.setAssets(self._current_assets)

    def setFilterText(self, text):
        """Set the filter text programmatically.

        Args:
            text (str): Filter text
        """
        self._search_box.setText(text)
        # Signal will be emitted automatically

    def setSubTypeFilter(self, sub_type):
        """Sets the sub-type filter for asset display.

        Filters the asset library to show only assets of the specified sub-type.
        This method updates both the internal state and the underlying data model.

        Args:
            sub_type (str): The sub-type to filter by ('eyebrow', 'hair', 'beard')
                          or None to show all assets.
        """
        if self._current_sub_type_filter != sub_type:
            self._current_sub_type_filter = sub_type

            # Apply filter to the data model
            if self._model:
                self._model.setSubTypeFilter(sub_type)
            else:
                # store for later application
                self._pending_sub_type_filter = sub_type

    def getCurrentSubTypeFilter(self):
        """Gets the current sub-type filter state.

        Returns:
            str or None: The current sub-type filter ('eyebrow', 'hair', 'beard')
                        or None if no filter is applied.
        """
        return self._current_sub_type_filter

    def getSelectedAsset(self):
        """Get the currently selected asset.

        Returns:
            dict or None: Selected asset data
        """
        if self._view:
            return self._view.getSelectedAsset()
        return None

    def selectAssetById(self, asset_id):
        """Select an asset by its ID.

        Args:
            asset_id (str): Asset ID to select

        Returns:
            bool: True if asset was found and selected
        """
        if self._view:
            return self._view.selectAssetById(asset_id)
        return False

    def clearSelection(self):
        """Clear the current selection."""
        if self._view:
            self._view.clearSelection()

    def setTitle(self, title):
        """Set the title of the asset library.

        Args:
            title (str): New title
        """
        if self._title_label:
            self._title_label.setText(title)

    def getScale(self):
        """Get the current scale factor.

        Returns:
            float: Current scale factor
        """
        if self._delegate:
            return self._delegate.getScale()
        return 1.0

    def setScale(self, scale_factor):
        """Set the scale factor.

        Args:
            scale_factor (float): Scale factor (0.5 to 2.0)
        """
        # Clamp and convert to slider value
        clamped_scale = max(0.5, min(2.0, scale_factor))
        slider_value = int(clamped_scale * 100)
        self._scale_slider.setValue(slider_value)
        # The slider signal will handle the actual scaling

    # Compatibility methods for BaseHairTab interface

    def refresh(self):
        """Refresh the asset library with the latest data from manager.

        This method provides compatibility with the existing BaseHairTab interface.
        """
        if self._hair_manager:
            try:
                # Get assets from manager
                assets = self._hair_manager.get_assets(asset_type=self._hair_type)

                self._logger.debug(
                    "ResponsiveAssetLibrary refresh: Retrieved %d assets for type '%s'",
                    len(assets),
                    self._hair_type,
                )

                # Update assets
                self.updateAssets(assets)

            except Exception as e:
                self._logger.error(
                    "Error refreshing ResponsiveAssetLibrary: %s",
                    str(e),
                    exc_info=True,
                )
        else:
            self._logger.warning("No hair manager available for refresh")

    @property
    def assets(self):
        """Get the current assets list for compatibility.

        Returns:
            list: Current assets
        """
        return self._current_assets

    @assets.setter
    def assets(self, value):
        """Set the assets list for compatibility.

        Args:
            value (list): Assets to set
        """
        self.updateAssets(value)

    # Alias methods for different naming conventions
    def update_assets(self, assets):
        """Alias for updateAssets to support snake_case naming.

        Args:
            assets (list): List of asset dictionaries
        """
        return self.updateAssets(assets)

    # Debug methods for compatibility with original AssetLibrary
    def enable_visual_debug(self):
        """Enable visual debug mode - adds colored borders to show component boundaries.

        This method provides compatibility with the original AssetLibrary interface.
        For QListView implementation, we apply debug styling to the main components.
        """
        try:
            self._visual_debug_enabled = True

            # Set main container debug style - red border
            self.setStyleSheet(
                """
                ResponsiveAssetLibrary {
                    border: 3px solid #FF0000 !important;  /* Red - Main container boundary */
                    background-color: rgba(255, 0, 0, 0.1) !important;
                }
            """
            )

            # Set grid view debug style - blue border
            if hasattr(self, "_view"):
                self._view.setStyleSheet(
                    """
                    AssetGridView {
                        border: 2px solid #0000FF !important;  /* Blue - GridView boundary */
                        background-color: rgba(0, 0, 255, 0.1) !important;
                    }
                """
                )

            # Set search box debug style - green border
            if hasattr(self, "_search_box"):
                self._search_box.setStyleSheet(
                    """
                    QLineEdit {
                        border: 2px solid #00FF00 !important;  /* Green - Search box boundary */
                        background-color: rgba(0, 255, 0, 0.1) !important;
                    }
                """
                )

            # Set scale slider debug style - yellow border
            if hasattr(self, "_scale_slider"):
                self._scale_slider.setStyleSheet(
                    """
                    QSlider {
                        border: 2px solid #FFFF00 !important;  /* Yellow - Slider boundary */
                        background-color: rgba(255, 255, 0, 0.1) !important;
                    }
                """
                )

            # Force style update
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()

            self._logger.info("Visual debug mode enabled for ResponsiveAssetLibrary")
            self._logger.info(
                "Debug colors: 🔴 Red=Main Container, 🔵 Blue=GridView, 🟢 Green=Search, 🟡 Yellow=Slider",
            )
            self._logger.debug(
                f"library width = {self.width()}, {self.height()}, list view width = {self._view.width()},"
                f" {self._view.height()}",
            )

        except Exception as e:
            self._logger.error(f"Error enabling visual debug mode: {e}")

    def disable_visual_debug(self):
        """Disable visual debug mode - restore original styling.

        This method provides compatibility with the original AssetLibrary interface.
        """
        try:
            self._visual_debug_enabled = False

            # Restore original styles
            self.setStyleSheet("")

            if hasattr(self, "_view"):
                self._view.setStyleSheet("")

            if hasattr(self, "_search_box"):
                self._search_box.setStyleSheet("")

            if hasattr(self, "_scale_slider"):
                self._scale_slider.setStyleSheet("")

            # Force style update
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()

            self._logger.info("Visual debug mode disabled for ResponsiveAssetLibrary")

        except Exception as e:
            self._logger.error(f"Error disabling visual debug mode: {e}")

    def toggle_visual_debug(self):
        """Toggle visual debug mode on/off.

        This method provides compatibility with the original AssetLibrary interface.
        """
        if not hasattr(self, "_visual_debug_enabled"):
            self._visual_debug_enabled = False

        if self._visual_debug_enabled:
            self.disable_visual_debug()
        else:
            self.enable_visual_debug()

    def show_spacing_breakdown(self):
        """Show spacing breakdown analysis for debugging.

        This method provides compatibility with the original AssetLibrary interface.
        For QListView implementation, we show relevant spacing information.
        """
        try:
            if hasattr(self, "_view"):
                # Get current view properties
                spacing = self._view.spacing()
                grid_size = self._view.gridSize()
                item_size = self._delegate.sizeHint(None, None) if hasattr(self, "_delegate") else None

                self._logger.info("=== ResponsiveAssetLibrary Spacing Breakdown ===")
                self._logger.info(f"Grid spacing: {spacing}px")
                self._logger.info(
                    f"Grid size: {grid_size.width()}x{grid_size.height()}",
                )
                if item_size:
                    self._logger.info(
                        f"Item size: {item_size.width()}x{item_size.height()}",
                    )

                # Get view geometry
                view_rect = self._view.geometry()
                self._logger.info(
                    f"View size: {view_rect.width()}x{view_rect.height()}",
                )

                # Calculate effective spacing ratio
                if item_size and item_size.width() > 0:
                    spacing_ratio = spacing / item_size.width() * 100
                    self._logger.info(
                        f"Spacing ratio: {spacing_ratio:.1f}% of item width",
                    )

                self._logger.info("=== End Spacing Breakdown ===")
            else:
                self._logger.warning("Grid view not available for spacing breakdown")

        except Exception as e:
            self._logger.error(f"Error showing spacing breakdown: {e}")

    # State management interface for filter persistence and future extensions
    def getFilterState(self):
        """Gets the complete filter state for persistence or restoration.

        Collects all current filter settings including text search, sub-type filter,
        and UI state like scale factor. This enables saving and restoring the
        complete user interface state.

        Returns:
            dict: Dictionary containing all filter state information with keys:
                - text_filter: Current search text
                - sub_type_filter: Current sub-type filter
                - scale_factor: Current zoom/scale level
        """
        return {
            "text_filter": self._current_filter_text,
            "sub_type_filter": self._current_sub_type_filter,
            "scale_factor": self._scale_slider.value() / 100.0 if self._scale_slider else 1.0,
            # 'tag_filters': [],  # Reserved for future tag filtering functionality
        }

    def restoreFilterState(self, state):
        """Restores the filter state from a previously saved state dictionary.

        This method restores all filter settings and UI state from a dictionary
        that was previously returned by getFilterState(). Used for maintaining
        user preferences across sessions or tab switches.

        Args:
            state (dict): Dictionary containing filter state information.
        """
        if "text_filter" in state and self._search_box:
            self._search_box.setText(state["text_filter"])

        if "sub_type_filter" in state:
            self.setSubTypeFilter(state["sub_type_filter"])

        if "scale_factor" in state and self._scale_slider:
            self._scale_slider.setValue(int(state["scale_factor"] * 100))

    def clearAllFilters(self):
        """Clears all active filters and resets the view to show all assets.

        This method resets the search text, sub-type filter, and any other
        active filters to their default state, effectively showing all available assets.
        """
        # Clear text search filter
        if self._search_box:
            self._search_box.setText("")

        # Clear sub-type filter
        self.setSubTypeFilter(None)

        # Reserved for future tag filtering functionality
        # self.clearTagFilters()  # Reserved interface

    # Reserved interfaces for future tag filtering functionality
    def setTagFilters(self, tags):
        """Sets tag-based filters for asset display (reserved for future use).

        This method is reserved for future implementation of tag-based filtering,
        where assets can be filtered by multiple tags simultaneously.

        Args:
            tags (list): List of tag strings to filter by.
        """
        if hasattr(self._model, "setTagFilters"):
            self._model.setTagFilters(tags)

    def setCombinedFilteringEnabled(self, enabled):
        """Enables or disables combined filtering functionality (reserved for future use).

        This method is reserved for future implementation where multiple filter
        types (text, sub-type, tags) can be combined for more precise filtering.

        Args:
            enabled (bool): Whether to enable combined filtering.
        """
        if hasattr(self._model, "setCombinedFilteringEnabled"):
            self._model.setCombinedFilteringEnabled(enabled)
