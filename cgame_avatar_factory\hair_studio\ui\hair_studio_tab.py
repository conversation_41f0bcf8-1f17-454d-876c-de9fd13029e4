"""Hair Studio Tab Module.

This module provides the main tab widget for the Hair Studio tool, which serves as the container
for all hair-related functionality including card, XGen, and curve hair tools.
"""

# Import built-in modules
# Import standard library
import logging

# Import third-party modules
from dayu_widgets import MTabWidget
import maya.utils as maya_utils

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_TAB_INDEX
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CURVE
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_XGEN
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_CARD_TAB
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_CURVE_TAB
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_XGEN_TAB
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_CHANGING_TABS
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_HANDLING_COMPONENT_SELECTION
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_FAILED_TO_INITIALIZE_HAIR_STUDIO
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_FAILED_TO_SETUP_UI
from cgame_avatar_factory.hair_studio.constants import OBJECT_NAME_HAIR_STUDIO_TAB

# BaseHairTab and HairManager will be imported when needed to avoid circular imports and support lazy loading


class HairStudioTab(MTabWidget):
    """Main tab widget for the Hair Studio tool.

    This class serves as the container for all hair-related tabs including Card, XGen, and Curve tabs.
    It manages the tab switching and coordinates between different hair tools.

    Uses lazy loading to improve startup performance - tabs are only created when first accessed.
    """

    def __init__(self, parent=None):
        """Initialize the HairStudioTab.

        Args:
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(HairStudioTab, self).__init__(parent)
        self.setObjectName(OBJECT_NAME_HAIR_STUDIO_TAB)

        # No need for style wrapper - MTabWidget + dayu_theme.apply() is sufficient

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Lazy loading state management
        self._hair_manager = None  # Will be created when first needed
        self._tabs_created = {
            HAIR_TYPE_CARD: False,
            HAIR_TYPE_XGEN: False,
            HAIR_TYPE_CURVE: False,
        }
        self._tab_widgets = {
            HAIR_TYPE_CARD: None,
            HAIR_TYPE_XGEN: None,
            HAIR_TYPE_CURVE: None,
        }

        # Initialize UI components with placeholder tabs
        maya_utils.executeDeferred(self.setup_ui)

        # Connect signals
        self._connect_signals()

    def setup_ui(self):
        """Set up the user interface components.

        This method creates placeholder tabs that will be lazily loaded when first accessed.
        """
        # Set tab position to North (top) to match Face Sculpting Center
        self.setTabPosition(QtWidgets.QTabWidget.North)

        try:
            # Create placeholder tabs - actual content will be loaded on demand
            self._create_placeholder_tabs()

            # Set default tab
            self.setCurrentIndex(DEFAULT_TAB_INDEX)

            # Connect signals
            self.currentChanged.connect(self._on_tab_changed)

            self._logger.info("HairStudioTab UI setup completed with lazy loading")

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_FAILED_TO_SETUP_UI,
                str(e),
                exc_info=True,
            )
            QtWidgets.QMessageBox.critical(
                self,
                "Error",
                "{}: {}".format(ERROR_MSG_FAILED_TO_INITIALIZE_HAIR_STUDIO, str(e)),
            )

    def _create_placeholder_tabs(self):
        """Create placeholder tabs that will be replaced with actual content when accessed."""
        # Create simple placeholder widgets for each tab
        placeholder_card = self._create_placeholder_widget("Card Hair", "Loading Card Hair tools...")
        placeholder_xgen = self._create_placeholder_widget("XGen Hair", "Loading XGen Hair tools...")
        placeholder_curve = self._create_placeholder_widget("Curve Hair", "Loading Curve Hair tools...")

        # Add placeholder tabs
        self.addTab(placeholder_card, UI_TEXT_CARD_TAB)
        self.addTab(placeholder_xgen, UI_TEXT_XGEN_TAB)
        self.addTab(placeholder_curve, UI_TEXT_CURVE_TAB)

    def _create_placeholder_widget(self, title, message):
        """Create a placeholder widget with loading message.

        Args:
            title (str): Title for the placeholder
            message (str): Loading message to display

        Returns:
            QtWidgets.QWidget: Placeholder widget
        """
        # Import third-party modules
        from dayu_widgets import MLabel

        placeholder = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(placeholder)
        layout.setContentsMargins(20, 20, 20, 20)

        # Add title and loading message
        title_label = MLabel(title).h2()
        message_label = MLabel(message)

        layout.addStretch()
        layout.addWidget(title_label)
        layout.addWidget(message_label)
        layout.addStretch()

        # Center align
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        message_label.setAlignment(QtCore.Qt.AlignCenter)

        return placeholder

    def _connect_signals(self):
        """Connect signals between components."""
        # Hair manager signals will be connected when manager is first created
        # This is now handled in _connect_manager_signals() method
        pass

    def _on_tab_changed(self, index):
        """Handle tab change events with lazy loading.

        Args:
            index (int): Index of the newly selected tab
        """
        try:
            if index < 0:  # No tab selected
                return

            # Determine which hair type this tab represents
            hair_type = self._get_hair_type_by_index(index)
            if not hair_type:
                return

            # Lazy load the tab content if not already created
            if not self._tabs_created[hair_type]:
                self._create_actual_tab(index, hair_type)

            # Get the current tab (now guaranteed to be actual content)
            current_tab = self.widget(index)

            # Only proceed if we have a real BaseHairTab (not placeholder)
            if hasattr(current_tab, 'refresh_asset_library'):
                # Refresh the asset library for the current tab
                current_tab.refresh_asset_library()

                # Update the component list for the current tab type
                self._update_component_list()

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_CHANGING_TABS,
                str(e),
                exc_info=True,
            )

    def _get_hair_type_by_index(self, index):
        """Get hair type by tab index.

        Args:
            index (int): Tab index

        Returns:
            str: Hair type or None if invalid index
        """
        hair_types = [HAIR_TYPE_CARD, HAIR_TYPE_XGEN, HAIR_TYPE_CURVE]
        if 0 <= index < len(hair_types):
            return hair_types[index]
        return None

    def _create_actual_tab(self, index, hair_type):
        """Create the actual BaseHairTab content for the specified tab.

        Args:
            index (int): Tab index to replace
            hair_type (str): Type of hair tab to create
        """
        try:
            # Import here to avoid circular imports
            # Import local modules
            from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab

            # Initialize hair manager if not already done
            if self._hair_manager is None:
                # Import local modules
                from cgame_avatar_factory.hair_studio.manager import HairManager
                self._hair_manager = HairManager()
                self._connect_manager_signals()

            self._logger.info(f"Creating {hair_type} tab content...")

            # Create the actual BaseHairTab
            actual_tab = BaseHairTab(
                hair_type,
                self._hair_manager,
                self,
            )

            # Replace the placeholder with actual content
            old_widget = self.widget(index)
            self.removeTab(index)

            # Insert the new tab at the same position
            tab_text = self._get_tab_text_by_hair_type(hair_type)
            self.insertTab(index, actual_tab, tab_text)

            # Clean up old placeholder
            if old_widget:
                old_widget.deleteLater()

            # Mark as created and store reference
            self._tabs_created[hair_type] = True
            self._tab_widgets[hair_type] = actual_tab

            # Set current index back to the newly created tab
            self.setCurrentIndex(index)

            self._logger.info(f"{hair_type} tab created successfully")

        except Exception as e:
            self._logger.error(f"Failed to create {hair_type} tab: {e}", exc_info=True)
            # Keep the placeholder in case of error

    def _get_tab_text_by_hair_type(self, hair_type):
        """Get tab display text by hair type.

        Args:
            hair_type (str): Hair type

        Returns:
            str: Tab display text
        """
        tab_text_map = {
            HAIR_TYPE_CARD: UI_TEXT_CARD_TAB,
            HAIR_TYPE_XGEN: UI_TEXT_XGEN_TAB,
            HAIR_TYPE_CURVE: UI_TEXT_CURVE_TAB,
        }
        return tab_text_map.get(hair_type, hair_type.title())

    def _connect_manager_signals(self):
        """Connect hair manager signals when manager is first created."""
        if self._hair_manager:
            self._hair_manager.components_updated.connect(self._on_components_updated)
            self._hair_manager.component_selected.connect(self._on_component_selected)

    def _on_components_updated(self, components):
        """Handle updates to the component list.

        Args:
            components (list): List of HairComponent objects
        """
        try:
            # Update component list in the current tab
            current_tab = self.get_current_tab()
            if current_tab:
                current_tab.update_component_list(components)
        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST,
                str(e),
                exc_info=True,
            )

    def _on_component_selected(self, component):
        """Handle component selection changes.

        Args:
            component (HairComponent or None): The selected component, or None if deselected
        """
        try:
            # Update editor area in the current tab
            current_tab = self.get_current_tab()
            if current_tab:
                current_tab.set_selected_component(component)
        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_HANDLING_COMPONENT_SELECTION,
                str(e),
                exc_info=True,
            )

    def _update_component_list(self):
        """Update the component list for the current tab type."""
        try:
            current_tab = self.get_current_tab()
            if not current_tab:
                return

            # Get components filtered by the current tab type
            components = self._hair_manager.get_components()
            filtered_components = [comp for comp in components if comp.get("type") == current_tab.hair_type]

            # Update the component list
            current_tab.update_component_list(filtered_components)

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST,
                str(e),
                exc_info=True,
            )

    def get_current_tab(self):
        """Get the currently active tab.

        Returns:
            BaseHairTab: The currently active tab widget, or None if no tab is selected or not yet loaded
        """
        if self.currentIndex() < 0:
            return None

        current_widget = self.currentWidget()

        # Check if this is a real BaseHairTab (not a placeholder)
        if hasattr(current_widget, 'hair_type'):
            return current_widget

        # Return None if it's still a placeholder
        return None

    def get_hair_manager(self):
        """Get the hair manager instance.

        Returns:
            HairManager: The hair manager instance, or None if not yet initialized
        """
        return self._hair_manager

    def ensure_hair_manager(self):
        """Ensure hair manager is initialized and return it.

        Returns:
            HairManager: The hair manager instance
        """
        if self._hair_manager is None:
            # Import local modules
            from cgame_avatar_factory.hair_studio.manager import HairManager
            self._hair_manager = HairManager()
            self._connect_manager_signals()
        return self._hair_manager
