"""Right Panel Widget Module.

This module provides the right panel widget with two groups:
1. Deformer Info Group (2/3 space)
2. Affected Objects Group (1/3 space)
"""

# Import built-in modules
import logging
from typing import Optional

# Import third-party modules
# Import dayu widgets
from dayu_widgets import MPushButton

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN
from cgame_avatar_factory.hair_studio.constants import DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformer
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.property_slider_widget import PropertySliderWidget


class RightPanelWidget(QtWidgets.QWidget):
    """右侧面板组件：变形器信息和受影响对象"""

    # 定义信号
    curvature_changed = QtCore.Signal(int)  # curvate changed signal
    influence_range_changed = QtCore.Signal(int)  # 影响范围变化
    selecte_affected_faces = QtCore.Signal(str)  # 选择受影响面片
    on_set_affect_faces = QtCore.Signal()  # 设置变形器影响的面片

    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)

        # 存储原始面片数据（用于Maya API调用）
        self._raw_affected_faces = []

        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN / 2, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN / 2)

        # 创建两个组，垂直方向2:1比例
        info_group = self.create_deformer_info_section()
        objects_group = self.create_affected_objects_section()

        # 添加到主布局，设置拉伸因子
        main_layout.addWidget(info_group, 2)  # 2/3的空间
        main_layout.addWidget(objects_group, 1)  # 1/3的空间

    def create_deformer_info_section(self):
        """创建变形器信息区域"""
        info_group = QtWidgets.QGroupBox("变形器信息")
        info_layout = QtWidgets.QVBoxLayout(info_group)
        info_layout.setSpacing(DEFAULT_SPACING)
        info_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)

        # Add initial separator
        separator_start = self._create_separator()
        info_layout.addWidget(separator_start)

        # Add separator after info section
        separator1 = self._create_separator()
        info_layout.addWidget(separator1)

        # 使用封装的滑条组件
        self.curvature_param_widget = PropertySliderWidget(
            label_text="曲度参数：",
            min_value=0,
            max_value=10,
            default_value=5,
        )
        info_layout.addWidget(self.curvature_param_widget)

        # 影响范围滑条
        self.influence_range_widget = PropertySliderWidget(
            label_text="影响范围：",
            min_value=1,
            max_value=100,
            default_value=15,
        )
        info_layout.addWidget(self.influence_range_widget)

        # Add stretch to push content to top
        info_layout.addStretch()

        return info_group

    def create_affected_objects_section(self):
        """创建受影响对象区域"""
        objects_group = QtWidgets.QGroupBox("调整受影响面片")
        objects_layout = QtWidgets.QVBoxLayout(objects_group)
        objects_layout.setSpacing(DEFAULT_SPACING)
        objects_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)

        # Add separator
        separator2 = self._create_separator()
        objects_layout.addWidget(separator2)

        # ===== 受影响面片选择 =====
        # Set affected objects button (合并功能：选择受影响的面片 + 设置影响面片)
        self.set_affect_faces_btn = MPushButton("设置影响面片")
        self.set_affect_faces_btn.setToolTip("请选择受影响的面片来调整驱动影响范围")
        objects_layout.addWidget(self.set_affect_faces_btn)

        # Add stretch to push content to top
        objects_layout.addStretch()

        return objects_group

    def _create_separator(self):
        """创建分隔线"""
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #cccccc; }")
        return separator

    def setup_connections(self):
        """设置信号连接"""
        # 连接滑条组件的信号
        self.curvature_param_widget.valueChanged.connect(self.curvature_changed.emit)
        self.influence_range_widget.valueChanged.connect(self.influence_range_changed.emit)

        # 连接按钮信号
        self.set_affect_faces_btn.clicked.connect(self.on_set_affect_faces)

    def on_set_affect_faces(self):
        """处理设置影响面片按钮点击（合并功能：选择受影响的面片 + 设置影响面片）"""
        print(f"[EVENT] Set Affect Faces button clicked (merged functionality)")

        # 先触发选择受影响面片的信号，然后触发设置影响面片的信号
        # UI事件管理器会处理这个串联逻辑
        self.selecte_affected_faces.emit("")

    def update_affected_faces_display(self, faces: str, raw_faces: list = None):
        """更新受影响面片显示（由UI事件管理器调用）

        Args:
            faces: 格式化后的显示文本
            raw_faces: 原始面片名称列表（用于Maya API调用）
        """
        if faces:
            # 存储原始数据（如果提供）
            if raw_faces is not None:
                self._raw_affected_faces = raw_faces if isinstance(raw_faces, list) else []
                self._logger.info(f"存储原始面片数据: {len(self._raw_affected_faces)} 个面片")

            self._logger.info(f"Updated affected faces display: {faces}")
        else:
            self._logger.warning("未提供有效的面片数据")

    def update_deformer_info(self, deformer: Optional[GlobalDeformer]):
        """更新变形器信息显示"""
        self._logger.debug(f"[UI] Updating deformer info panel")

        if deformer is None:
            self._logger.debug(f"[UI] Clearing deformer info panel")
            # 清除信息显示
            self.curvature_param_widget.setValue(0)
            self.influence_range_widget.setValue(15)
            self._raw_affected_faces = []
            return

        # 更新变形器信息显示（第一部分：属性信息）
        info_text = f"驱动名: {deformer.name}\n"
        info_text += f"曲线名: {deformer.curve_data or '未设置'}\n"
        info_text += f"模型名: {deformer.binding_mesh or '未设置'}\n"
        info_text += f"状态: {deformer.status.value}\n"
        info_text += f"创建时间: {deformer.created_time}"
        self._logger.debug(f"[UI] Updating deformer info text: {info_text}")

        # 更新属性滑块（第二部分：属性参数）
        self.curvature_param_widget.setValue(deformer.curvature)
        self.influence_range_widget.setValue(int(deformer.influence_range))

        # 更新受影响对象数据（存储到内部变量）
        if deformer.affected_mesh_faces:
            self._raw_affected_faces = deformer.affected_mesh_faces
        else:
            self._raw_affected_faces = []

        self._logger.debug(f"[UI] Deformer info panel updated successfully")

    def get_affected_objects_from_ui(self):
        """从UI组件中获取受影响对象列表（返回原始数据用于Maya API）

        Returns:
            list: 原始面片名称列表，如果没有数据则返回空列表
        """
        # 优先返回存储的原始数据
        if self._raw_affected_faces:
            self._logger.info(f"从UI获取受影响对象（原始数据）: {len(self._raw_affected_faces)} 个面片")
            return self._raw_affected_faces

        # 如果没有原始数据，返回空列表
        self._logger.warning("没有存储的原始面片数据")
        return []

    def clear_affected_objects_display(self):
        """清空受影响对象显示"""
        self._raw_affected_faces = []  # 清空原始数据
        self._logger.info("已清空受影响对象显示")
