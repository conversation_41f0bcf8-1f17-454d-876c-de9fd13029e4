"""Global Deformer Maya API Module.

给mannager处理核心逻辑
"
"""
# Import built-in modules
from functools import partial
import traceback

# Import third-party modules
import maya.cmds as cmds
import maya.mel as mel

# Import local modules
import cgame_avatar_factory.hair_studio.constants as const
import cgame_avatar_factory.hair_studio.maya_api.global_deformer.core_fun as core_fun

############################################## public fun for ui #########################################


def select_deformer_data(curve_name):
    if not cmds.objExists(curve_name):
        cmds.error("curve = {} not exists".format(curve_name))
        return

    cmds.select(curve_name)


def on_affect_range_btn(deformer_name, value):
    core_fun.set_deformer_range(deformer_name, value)


def on_driver_curvature_btn(deformer_name, value):
    # TODO: combine all attribute
    CURV_SCALE = 0.1
    cmds.setAttr("{}.rotation".format(deformer_name), value * CURV_SCALE)


def on_select_for_curve():
    seles = core_fun.get_seles_long()
    if seles is None:
        cmds.error("no seles for on_select_for_curve!")
        return None
    return seles


def select_hair_curve_to_bind():
    curve_node, mesh_node, _ = core_fun.resolve_curve_mesh_from_sel()

    return mesh_node, curve_node


def delete_driver(driver_name, curve_name):
    try:
        if cmds.objExists(curve_name):
            cmds.delete(curve_name)
        if cmds.objExists(driver_name):
            cmds.delete(driver_name)
        return True
    except Exception as e:
        cmds.error(f"Error deleting driver: {e}")
        return False


def on_freeze_btn(deformer_name, mesh_name, curve_name):
    is_success = False
    try:
        cmds.delete(mesh_name, constructionHistory=True)
        if cmds.objExists(curve_name):
            cmds.delete(curve_name)
        if cmds.objExists(deformer_name):
            cmds.delete(deformer_name)
        is_success = True
    except Exception as e:
        cmds.error(f"Error deleting driver: {e}")
        is_success = False

    return is_success


def select_hair_face_set_weight():
    seles = core_fun.get_seles_long()
    if seles is None:
        cmds.error("no seles for select_hair_face_set_weight!")
        return None

    return seles


def on_create_curve_btn():
    # TODO: standard way to get seles from ui, then create.
    generated_curve = core_fun.create_curve_for_sels()
    if not generated_curve:
        cmds.error("create curve failed!")
        return None
    return generated_curve


def create_driver(curve, mesh, driver_name="driver"):
    # 注意：这里没有数据管理器，所以不处理proximity data
    driver_name = core_fun.create_deformer_maya(mesh, curve)
    return True, driver_name


def on_set_affected_face_btn(deformer, faces):
    if not core_fun.set_affected_face(deformer, faces):
        cmds.error("on_set_affected_face_btn failed!")
        return False

    return True


############################################## public fun for ui #########################################

############################################## middle fun for mesh proximity wrap #########################################


def save_proximity_data(mesh_name):
    """保存mesh的proximity data"""
    if not mesh_name or not cmds.objExists(mesh_name):
        cmds.error(f"save_proximity_data: mesh {mesh_name} does not exist")
        return None

    try:
        # NOTE: find proximity and bs node
        proximity_wrap_node = cmds.listConnections(mesh_name, type="proximityWrap")
        bs_node = cmds.listConnections(mesh_name, type="blendShape")

        proximity_node = proximity_wrap_node[0] if proximity_wrap_node else None
        blend_shape_node = bs_node[0] if bs_node else None

        # 只有在找到至少一个节点时才返回数据
        if proximity_node or blend_shape_node:
            return (proximity_node, blend_shape_node)
        else:
            cmds.error(f"save_proximity_data: no proximity or blendShape nodes found for {mesh_name}")
            return None

    except Exception as e:
        cmds.error(f"Error in save_proximity_data for {mesh_name}: {e}")
        return None


def resume_proximity_data(mesh_name, proximity_wrap_node, bs_node):
    """恢复mesh的proximity data"""
    if not mesh_name or not cmds.objExists(mesh_name):
        cmds.error(f"resume_proximity_data: mesh {mesh_name} does not exist")
        return False

    try:
        restored_any = False

        # 恢复proximity wrap连接
        if proximity_wrap_node and cmds.objExists(proximity_wrap_node):
            try:
                # 检查连接是否已存在
                existing_connections = cmds.listConnections(f"{mesh_name}.inMesh", source=True, plugs=True)
                if not existing_connections or f"{proximity_wrap_node}.outputGeometry[0]" not in existing_connections:
                    cmds.connectAttr(f"{proximity_wrap_node}.outputGeometry[0]", f"{mesh_name}.inMesh", force=True)

                    restored_any = True
                else:
                    cmds.warning(f"Proximity wrap connection already exists for {mesh_name}")
            except Exception as e:
                cmds.error(f"Error restoring proximity wrap connection: {e}")

        # 恢复blend shape连接
        if bs_node and cmds.objExists(bs_node):
            try:
                # 检查连接是否已存在
                existing_connections = cmds.listConnections(f"{mesh_name}.inMesh", source=True, plugs=True)
                if not existing_connections or f"{bs_node}.outputGeometry[0]" not in existing_connections:
                    cmds.connectAttr(f"{bs_node}.outputGeometry[0]", f"{mesh_name}.inMesh", force=True)

                    restored_any = True
                else:
                    cmds.warning(f"Blend shape connection already exists for {mesh_name}")
            except Exception as e:
                cmds.error(f"Error restoring blend shape connection: {e}")

        if not restored_any:
            cmds.warning(f"No proximity data to restore for {mesh_name}")

        return True

    except Exception as e:
        cmds.error(f"Error resuming proximity data for {mesh_name}: {e}")
        return False


############################################## middle fun for mesh proximity wrap #########################################

############################################## public fun for menu callback #########################################
def adjust_curve_deformer_weight():
    """调整曲线变形器权重（从Maya菜单调用）"""

    # 获取菜单管理器实例
    # Import local modules
    from cgame_avatar_factory.hair_studio.maya_api.global_deformer.interactive_menu import AllViewportObjectMenuManager

    maya_menu_manager = AllViewportObjectMenuManager()

    try:
        sel_curve = cmds.ls(sl=1, long=1)[0]
        # if cv convert to mesh
        sel_curve = sel_curve.split(".")[0]

        # checkout output wire deformer
        shape_node = cmds.listRelatives(sel_curve, shapes=True, f=True)[0]

        des_nodes = cmds.listConnections(shape_node, d=1)

        deformer = None
        for node in des_nodes:
            if cmds.objectType(node) == "wire":
                deformer = node
                break
        if not deformer:
            cmds.warning("No wire deformer found for curve: {}".format(sel_curve))
            return

        # 如果有数据管理器，尝试找到对应的GlobalDeformer并更新
        if maya_menu_manager.middle_ui_event_manager:
            # 通过曲线名称查找对应的GlobalDeformer
            matching_deformer = None
            for global_deformer in maya_menu_manager.middle_ui_event_manager.data_manager.get_all_deformers():
                if global_deformer.curve_data == sel_curve:
                    matching_deformer = global_deformer
                    break

            if matching_deformer:
                # 可以在这里更新GlobalDeformer的状态或触发UI更新
                maya_menu_manager.middle_ui_event_manager.handle_deformer_selection_event(matching_deformer.name)
            else:
                cmds.warning(f"[警告] 未找到曲线 {sel_curve} 对应的GlobalDeformer")
        else:
            cmds.warning("[警告] 数据管理器不可用，无法同步UI状态")

        # find deformer down node
        deformer_mesh = cmds.listConnections("{0}.outputGeometry".format(deformer), d=1)[0]
        # make sure deformer_mesh is a mesh
        if not deformer_mesh or not _is_mesh(deformer_mesh):
            cmds.warning(
                "[警告] 请选择正确的曲线！curve find wire deformer={0} outputGeometry = {1} is not a mesh!".format(
                    deformer,
                    deformer_mesh,
                ),
            )
            return

        _show_face_selection_ui(deformer, deformer_mesh)

    except Exception as e:
        cmds.warning(f"调整曲线变形器权重失败: {e}")


def _is_mesh(obj):
    if cmds.objectType(obj) == "mesh":
        return True
    shape_node = cmds.listRelatives(obj, shapes=True, f=True)
    if shape_node and cmds.objectType(shape_node[0]) == "mesh":
        return True
    return False


def _show_face_selection_ui(deformer, deformer_mesh):
    """显示面片选择UI"""
    cmds.inViewMessage(amg='请选择受影响面，并按下中间的"结束选择"键结束', pos="bottomCenter", fade=True)

    if cmds.headsUpDisplay("HUDHelloButton", exists=True):
        cmds.headsUpDisplay("HUDHelloButton", remove=True)

    def end_select(deformer, *args):
        sels = cmds.ls(sl=1, long=1)

        if not core_fun.set_affected_face(deformer, sels):
            cmds.warning("set_affected_face failed")
        # finally need to remove HUD any way
        cmds.headsUpDisplay("HUDHelloButton", remove=True)

    cmds.hudButton(
        "HUDHelloButton",
        s=7,
        b=7,
        vis=1,
        l="结束选择",
        blockSize="medium",
        bw=120,
        bsh="roundRectangle",
        rc=partial(end_select, deformer),
    )
    cmds.select(clear=True)
    mel.eval(
        """
        doMenuComponentSelection("{0}", "meshUVShell");
        """.format(
            deformer_mesh
        )
    )


def create_curve_on_menu():
    """交互式创建曲线（从Maya菜单调用）"""
    return core_fun.create_curve_for_sels()


def create_deformer_on_menu():
    """交互式创建变形器（从Maya菜单调用）"""

    # 获取菜单管理器实例
    # Import local modules
    from cgame_avatar_factory.hair_studio.maya_api.global_deformer.interactive_menu import AllViewportObjectMenuManager

    maya_menu_manager = AllViewportObjectMenuManager()

    if not maya_menu_manager.middle_ui_event_manager:
        cmds.warning("[警告] 未找到UI事件管理器，使用传统方式创建变形器")
        return _create_deformer_traditional()

    try:
        # 读取选择，要求必须一个curve、一个mesh（mesh也可以是组件如face/edge/vertex）
        curve_node, mesh_node, mesh_faces = core_fun.resolve_curve_mesh_from_sel()
        if not curve_node or not mesh_node:
            cmds.warning("[错误] 未找到有效的曲线和网格")
            return None

        # 通过UI事件管理器的数据管理器添加到系统中
        data_manager = maya_menu_manager.middle_ui_event_manager.data_manager
        if data_manager:
            # 使用数据管理器的proximity保护装饰器
            @data_manager.with_proximity_protection(mesh_node, "create")
            def protected_create_maya():
                return core_fun.create_deformer_maya(mesh_node, curve_node, mesh_faces)

            deformer_name = protected_create_maya()

            if not deformer_name:
                cmds.error("[错误] Maya变形器创建失败")
                return None
            # 创建GlobalDeformer对象（跳过Maya API调用，因为已经完成）
            success, deformer = data_manager.create_deformer_from_existing(
                curve_data=curve_node,
                binding_mesh=mesh_node,
                name=deformer_name,
                affected_faces=mesh_faces,
            )

            if success:
                # 添加到数据管理器（会自动触发UI更新）
                add_success = data_manager.add_deformer(deformer)
                if add_success:
                    # 设置为当前选择
                    data_manager.select_deformer(deformer_name)
                    return deformer_name
                else:
                    cmds.error(f"[错误] 无法添加变形器到数据管理器: {deformer_name}")
            else:
                cmds.error(f"[错误] 无法创建GlobalDeformer对象: {deformer_name}")
        else:
            cmds.error("[错误] 数据管理器不可用")

    except Exception as e:
        cmds.error(f"[错误] 交互式创建变形器失败: {e}")
        traceback.print_exc()

    return None


def _create_deformer_traditional():
    """传统方式创建变形器（不集成UI系统）"""

    try:
        curve_node, mesh_node, mesh_faces = core_fun.resolve_curve_mesh_from_sel()
        # 直接调用core_fun创建变形器
        deformer_name = core_fun.create_deformer_maya(mesh_node, curve_node, mesh_faces)
        return deformer_name
    except Exception as e:
        cmds.error(f"[错误] 传统方式创建变形器失败: {e}")
        return None


def save_proximity_data(mesh_name):
    """保存mesh的proximity数据

    Args:
        mesh_name (str): mesh名称

    Returns:
        Any: 保存的proximity数据

    Note:
        这是一个占位符函数，用户需要根据实际需求实现
        例如：保存mesh的变形状态、权重信息等
    """
    all_nodes = cmds.listHistory(mesh_name)
    proximity = None
    for node in all_nodes:
        if cmds.objectType(node) == "proximityWrap":
            proximity = node
            break
    reference_head = cmds.listConnections(
        "{}.drivers[0].driverGeometry".format(proximity),
        source=True,
        destination=False,
    )[0]
    if not reference_head:
        cmds.error(f"[PROXIMITY] 未找到参考头模型: {mesh_name}")
        return None
    # delete history for hair_target
    cmds.delete(mesh_name, constructionHistory=True)
    cmds.delete(reference_head, constructionHistory=True)
    return {
        "mesh_name": mesh_name,
        "head_mesh": reference_head,
        "proximity": proximity,
    }


def resume_proximity_data(mesh_name, proximity_data):
    """恢复mesh的proximity数据

    Args:
        mesh_name (str): mesh名称
        proximity_data (Any): 之前保存的proximity数据

    Note:
        这是一个占位符函数，用户需要根据实际需求实现
        例如：恢复mesh的变形状态、权重信息等
    """

    # TODO: 用户需要在这里实现实际的数据恢复逻辑
    hair_head_mesh = proximity_data["head_mesh"]
    hair_target = mesh_name

    # Import local modules
    import cgame_avatar_factory.hair_studio.maya_api.surface_wrap_mesh as surface_wrap_mesh

    proximity_wrap_parm = const.PROXIMITY_WRAP_MODES.get("hair", const.SURFACE_PRO_WRAP_PARAMS)
    wrap_node_name = surface_wrap_mesh.createProximityWrap(
        hair_head_mesh,
        [hair_target],
        proximity_data["proximity"],
        proximity_wrap_parm,
    )
    # Import local modules
    from cgame_avatar_factory.hair_studio.maya_api.load_maya_asset import WrapHairMesh

    # NOTE: make sure real head change will update to hair base head mesh;
    bs_node = WrapHairMesh.create_blendshape(
        "{0}_bs".format(hair_head_mesh),
        const.HAIR_MATCH_HEAD_NAME,
        hair_head_mesh,
    )

    return True


############################################## public fun for menu callback #########################################
