"""Data Manager for Hair Studio.

This module provides data management for Hair Studio, loading assets from
specified paths and managing hair components and assets.
"""

# Import built-in modules
import hashlib
import logging
import os
import threading
import time
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
import uuid

# Define constants locally to avoid circular imports
HAIR_TYPE_CARD = "card"
HAIR_TYPE_XGEN = "xgen"
HAIR_TYPE_CURVE = "curve"

DEFAULT_COMPONENT_WIDTH = 1.0
DEFAULT_COMPONENT_HEIGHT = 2.0
DEFAULT_XGEN_DENSITY = 1000
DEFAULT_XGEN_LENGTH = 5.0
DEFAULT_CURVE_THICKNESS = 0.1
DEFAULT_CURVE_SUBDIVISIONS = 8

PROPERTY_NAME = "name"
PROPERTY_VISIBLE = "visible"
PROPERTY_WIDTH = "width"
PROPERTY_HEIGHT = "height"
PROPERTY_DENSITY = "density"
PROPERTY_LENGTH = "length"
PROPERTY_THICKNESS = "thickness"
PROPERTY_SUBDIVISIONS = "subdivisions"

# Import local modules
# Import asset configuration constants from hair_studio constants
from cgame_avatar_factory.hair_studio.constants import SUPPORTED_MODEL_EXTENSIONS
from cgame_avatar_factory.hair_studio.constants import SUPPORTED_THUMBNAIL_EXTENSIONS

# Import asset configuration functions


def _find_reference_asset_in_directory(directory_path: str, logger) -> Optional[str]:
    """Find reference asset (head model) in a specific directory.

    Args:
        directory_path (str): Directory path to search in
        logger: Logger instance for logging

    Returns:
        Optional[str]: Path to the reference asset, or None if not found
    """
    logger = logger or logging.getLogger(__name__)
    # Supported reference model extensions
    reference_extensions = {".obj", ".fbx"}

    if not os.path.exists(directory_path):
        return None

    try:
        # List all files in the directory
        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)

            # Skip if not a file
            if not os.path.isfile(file_path):
                continue

            # Check file extension
            _, ext = os.path.splitext(filename)
            if ext.lower() not in reference_extensions:
                continue

            # Check if filename contains "head" (case-insensitive)
            prefix = filename.split(".")[0]
            if "head" == prefix.lower():
                return file_path

    except Exception as e:
        logger.warning(f"Error searching for reference asset in {directory_path}: {e}")

    return None


def _find_asset_in_directory(directory_path: str, directory_name: str) -> tuple:
    """Find model and thumbnail files in a directory.

    Args:
        directory_path (str): Path to the directory to search
        directory_name (str): Name of the directory (used for matching files)

    Returns:
        tuple: (model_path, thumbnail_path) or (None, None)
    """
    logger = logging.getLogger(__name__)

    # Look for 3D model file inside the directory with the same name
    model_path = None
    for ext in SUPPORTED_MODEL_EXTENSIONS:
        potential_model = os.path.join(directory_path, f"{directory_name}{ext}")
        if os.path.isfile(potential_model):
            model_path = potential_model
            break

    # Look for thumbnail file - first try inside the directory (new structure)
    thumbnail_path = None
    for ext in SUPPORTED_THUMBNAIL_EXTENSIONS:
        # New structure: thumbnail inside the directory
        potential_thumbnail = os.path.join(directory_path, f"{directory_name}{ext}")
        if os.path.isfile(potential_thumbnail):
            thumbnail_path = potential_thumbnail
            break

    # If no thumbnail found inside, try parent directory (old structure compatibility)
    if not thumbnail_path:
        parent_dir = os.path.dirname(directory_path)
        for ext in SUPPORTED_THUMBNAIL_EXTENSIONS:
            potential_thumbnail = os.path.join(parent_dir, f"{directory_name}{ext}")
            if os.path.isfile(potential_thumbnail):
                thumbnail_path = potential_thumbnail
                logger.debug(f"Found thumbnail in parent directory: {potential_thumbnail}")
                break

    return model_path, thumbnail_path


def _scan_assets_in_directory_with_subtype(
    directory_path: str,
    asset_type: str,
    sub_asset_type: str,
) -> List[Dict[str, Any]]:
    """Scan a directory for hair assets with known sub-type information.

    This is an optimized version that doesn't need to determine sub-type from path
    since the sub-type is already known from the configuration system.

    Args:
        directory_path (str): Path to the directory to scan
        asset_type (str): Type of assets (card, xgen, curve)
        sub_asset_type (str): Known sub-type (eyebrow, hair, beard)

    Returns:
        List[Dict[str, Any]]: List of asset dictionaries with file_path and thumbnail
    """
    # Always use this module's own logger
    logger = logging.getLogger(__name__)

    assets = []

    if not os.path.exists(directory_path):
        logger.warning(f"Directory does not exist: {directory_path}")
        return assets

    try:
        # First, find base reference in current directory
        base_reference = _find_reference_asset_in_directory(directory_path, logger)
        if not base_reference:
            logger.error(f"No reference asset found in base directory: {directory_path}")
            return assets  # Skip entire directory if no base reference

        # Collect all potential asset directories from both level 1 and level 2
        potential_asset_dirs = []

        # Level 1: Direct subdirectories
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isdir(item_path):
                potential_asset_dirs.append((item_path, item, base_reference))  # Add base reference

                # Level 2: Subdirectories within subdirectories
                try:
                    # Check if this level has its own reference (can override base)
                    level_reference = _find_reference_asset_in_directory(item_path, logger)
                    current_reference = level_reference if level_reference else base_reference

                    for sub_item in os.listdir(item_path):
                        sub_item_path = os.path.join(item_path, sub_item)

                        if os.path.isdir(sub_item_path):
                            # deeper find reference
                            _sub_item_reference = _find_reference_asset_in_directory(sub_item_path, logger)
                            if _sub_item_reference:
                                logger.debug(
                                    f"Found reference asset level = 3 in {sub_item_path}: {_sub_item_reference}",
                                )

                                current_reference = _sub_item_reference

                            potential_asset_dirs.append((sub_item_path, sub_item, current_reference))
                except (PermissionError, OSError) as e:
                    logger.warning(f"Cannot access subdirectory {item_path}: {e}")
                    continue

        # Check each potential directory for valid assets
        for dir_path, dir_name, reference_path in potential_asset_dirs:
            model_path, thumbnail_path = _find_asset_in_directory(dir_path, dir_name)

            # Only create asset if both thumbnail and model exist
            if thumbnail_path and model_path:
                # Generate unique ID using hash of model file path
                asset_id = hashlib.md5(model_path.encode()).hexdigest()[:8]

                # Create display name from directory name
                display_name = dir_name.replace("_", " ").title()

                # Create metadata with reference path
                metadata = {
                    "file_path": model_path,
                    "reference": reference_path,
                }

                asset = {
                    "id": f"{asset_type}_{asset_id}",
                    "name": display_name,
                    "asset_type": asset_type,
                    "sub_asset_type": sub_asset_type,  # Use provided sub-type (no path parsing needed)
                    "file_path": model_path,
                    "thumbnail": thumbnail_path,
                    "metadata": metadata,
                }
                assets.append(asset)

    except (PermissionError, OSError) as e:
        logger.error(f"Error scanning directory {directory_path}: {e}")

    return assets


class MockHairComponent:
    """Mock hair component for testing and demonstration."""

    def __init__(
        self,
        component_id: str,
        name: str,
        hair_type: str,
        asset_id: str = None,
    ):
        """Initialize a mock hair component.

        Args:
            component_id (str): Unique identifier for the component
            name (str): Display name of the component
            hair_type (str): Type of hair (card, xgen, curve)
            asset_id (str, optional): ID of the source asset
        """
        self.id = component_id
        self.name = name
        self.type = hair_type
        self.asset_id = asset_id
        self.visible = True
        self.is_viewed = True  # Add is_viewed property for UI display

        # Type-specific properties
        if hair_type == HAIR_TYPE_CARD:
            self.width = DEFAULT_COMPONENT_WIDTH
            self.height = DEFAULT_COMPONENT_HEIGHT
        elif hair_type == HAIR_TYPE_XGEN:
            self.density = DEFAULT_XGEN_DENSITY
            self.length = DEFAULT_XGEN_LENGTH
        elif hair_type == HAIR_TYPE_CURVE:
            self.thickness = DEFAULT_CURVE_THICKNESS
            self.subdivisions = DEFAULT_CURVE_SUBDIVISIONS

    def to_dict(self) -> Dict[str, Any]:
        """Convert component to dictionary representation.

        Returns:
            Dict[str, Any]: Component data as dictionary
        """
        data = {
            "id": self.id,
            PROPERTY_NAME: self.name,
            "type": self.type,
            "asset_id": self.asset_id,
            PROPERTY_VISIBLE: self.visible,
            "is_viewed": self.is_viewed,
        }

        # Add type-specific properties
        if self.type == HAIR_TYPE_CARD:
            data[PROPERTY_WIDTH] = self.width
            data[PROPERTY_HEIGHT] = self.height
        elif self.type == HAIR_TYPE_XGEN:
            data[PROPERTY_DENSITY] = self.density
            data[PROPERTY_LENGTH] = self.length
        elif self.type == HAIR_TYPE_CURVE:
            data[PROPERTY_THICKNESS] = self.thickness
            data[PROPERTY_SUBDIVISIONS] = self.subdivisions

        return data


class DataManager:
    """Data manager for Hair Studio asset and component management with async loading support."""

    def __init__(self):
        """Initialize the data manager."""
        self._components: List[MockHairComponent] = []
        self._assets: List[Dict[str, Any]] = []
        self._selected_component_id: Optional[str] = None

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Async loading state management
        self._loading_complete = False
        self._loading_in_progress = False
        self._loading_thread = None
        self._loading_callbacks: List[Callable[[List[Dict[str, Any]]], None]] = []

        # Batch loading configuration
        self._batch_size = 50  # Load assets in batches of 50
        self._batch_delay = 0.01  # Small delay between batches to prevent UI freezing

        # Initialize with empty component list as per PRD requirements
        # Components should only be created when user drags assets from library

        # Start async asset loading
        self._start_async_loading()

    def _start_async_loading(self):
        """Start asynchronous asset loading in a background thread."""
        if self._loading_in_progress:
            return

        self._loading_in_progress = True
        self._logger.info("Starting async asset loading...")

        # Start loading in a separate thread
        self._loading_thread = threading.Thread(
            target=self._async_load_assets,
            daemon=True,
            name="HairStudio-AssetLoader"
        )
        self._loading_thread.start()

    def _async_load_assets(self):
        """Load assets asynchronously in batches."""
        try:
            start_time = time.time()

            # Use new asset library configuration to get paths with sub-type information
            # Import local modules
            from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import get_asset_lib_config

            config = get_asset_lib_config()
            all_assets = []

            # For card type, get paths organized by sub-type
            if config.get_tab_count() > 0:
                # Load card assets with sub-type information
                for tab_key in config.get_tab_keys():
                    tab_paths = config.get_tab_lib_paths(tab_key)
                    for directory_path in tab_paths:
                        # Pass sub-type information to scanning function
                        assets = _scan_assets_in_directory_with_subtype(directory_path, "card", tab_key)
                        all_assets.extend(assets)

                        # Process in batches to avoid UI freezing
                        if len(all_assets) >= self._batch_size:
                            self._process_asset_batch(all_assets[:self._batch_size])
                            all_assets = all_assets[self._batch_size:]
                            time.sleep(self._batch_delay)  # Small delay to prevent UI blocking

            # Process remaining assets
            if all_assets:
                self._process_asset_batch(all_assets)

            # Mark loading as complete
            self._loading_complete = True
            self._loading_in_progress = False

            load_time = time.time() - start_time
            self._logger.info(f"Async asset loading completed in {load_time:.2f}s. Total assets: {len(self._assets)}")

            # Notify callbacks
            self._notify_loading_complete()

        except Exception as e:
            self._logger.error(f"Error during async asset loading: {e}", exc_info=True)
            self._loading_in_progress = False

    def _process_asset_batch(self, batch_assets: List[Dict[str, Any]]):
        """Process a batch of assets and add them to the main list.

        Args:
            batch_assets: List of asset dictionaries to process
        """
        # Thread-safe asset addition
        self._assets.extend(batch_assets)
        self._logger.debug(f"Processed batch of {len(batch_assets)} assets. Total: {len(self._assets)}")

    def _notify_loading_complete(self):
        """Notify all registered callbacks that loading is complete."""
        for callback in self._loading_callbacks:
            try:
                callback(self._assets.copy())
            except Exception as e:
                self._logger.error(f"Error in loading callback: {e}", exc_info=True)

    def add_loading_callback(self, callback: Callable[[List[Dict[str, Any]]], None]):
        """Add a callback to be called when asset loading is complete.

        Args:
            callback: Function to call with loaded assets
        """
        self._loading_callbacks.append(callback)

        # If loading is already complete, call immediately
        if self._loading_complete:
            try:
                callback(self._assets.copy())
            except Exception as e:
                self._logger.error(f"Error in immediate callback: {e}", exc_info=True)

    def is_loading_complete(self) -> bool:
        """Check if asset loading is complete.

        Returns:
            bool: True if loading is complete
        """
        return self._loading_complete

    def wait_for_loading(self, timeout: float = 30.0) -> bool:
        """Wait for asset loading to complete.

        Args:
            timeout: Maximum time to wait in seconds

        Returns:
            bool: True if loading completed within timeout
        """
        if self._loading_complete:
            return True

        if self._loading_thread and self._loading_thread.is_alive():
            self._loading_thread.join(timeout)

        return self._loading_complete

    def _load_assets_from_paths(self):
        """Legacy synchronous loading method - now redirects to async loading."""
        if not self._loading_complete:
            self._logger.info("Synchronous loading requested - waiting for async loading to complete...")
            self.wait_for_loading()

        self._logger.debug(f"Assets available: {len(self._assets)} total assets")

    def reload_assets(self):
        """Reload assets from configured paths asynchronously."""
        self._logger.info("Reloading assets...")

        # Clear existing assets
        self._assets.clear()

        # Reset loading state
        self._loading_complete = False

        # Start new async loading
        self._start_async_loading()

    def get_components(self, hair_type: str = None) -> List[Dict[str, Any]]:
        """Get all components, optionally filtered by hair type.

        Args:
            hair_type (str, optional): Filter by hair type

        Returns:
            List[Dict[str, Any]]: List of component dictionaries
        """
        components = self._components
        if hair_type:
            components = [c for c in components if c.type == hair_type]

        return [c.to_dict() for c in components]

    def get_component(self, component_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific component by ID.

        Args:
            component_id (str): ID of the component to retrieve

        Returns:
            Optional[Dict[str, Any]]: Component data or None if not found
        """
        for component in self._components:
            if component.id == component_id:
                return component.to_dict()
        return None

    def create_component(self, asset_id: str) -> Optional[Dict[str, Any]]:
        """Create a new component from an asset.

        Args:
            asset_id (str): ID of the asset to create component from

        Returns:
            Optional[Dict[str, Any]]: Created component data or None if failed
        """
        # Find the asset
        asset = None
        for a in self._assets:
            if a["id"] == asset_id:
                asset = a
                break

        if not asset:
            return None

        # Create new component
        component_id = str(uuid.uuid4())
        component_name = f"New {asset['name']}"

        component = MockHairComponent(
            component_id=component_id,
            name=component_name,
            hair_type=asset["asset_type"],
            asset_id=asset_id,
        )

        self._components.append(component)
        return component.to_dict()

    def update_component(self, component_id: str, **kwargs) -> bool:
        """Update a component's properties.

        Args:
            component_id (str): ID of the component to update
            **kwargs: Properties to update

        Returns:
            bool: True if update successful, False otherwise
        """
        for component in self._components:
            if component.id == component_id:
                # Update basic properties
                if PROPERTY_NAME in kwargs:
                    component.name = kwargs[PROPERTY_NAME]
                if PROPERTY_VISIBLE in kwargs:
                    component.visible = kwargs[PROPERTY_VISIBLE]
                if "is_viewed" in kwargs:
                    component.is_viewed = kwargs["is_viewed"]

                # Update type-specific properties
                if component.type == HAIR_TYPE_CARD:
                    if PROPERTY_WIDTH in kwargs:
                        component.width = kwargs[PROPERTY_WIDTH]
                    if PROPERTY_HEIGHT in kwargs:
                        component.height = kwargs[PROPERTY_HEIGHT]
                elif component.type == HAIR_TYPE_XGEN:
                    if PROPERTY_DENSITY in kwargs:
                        component.density = kwargs[PROPERTY_DENSITY]
                    if PROPERTY_LENGTH in kwargs:
                        component.length = kwargs[PROPERTY_LENGTH]
                elif component.type == HAIR_TYPE_CURVE:
                    if PROPERTY_THICKNESS in kwargs:
                        component.thickness = kwargs[PROPERTY_THICKNESS]
                    if PROPERTY_SUBDIVISIONS in kwargs:
                        component.subdivisions = kwargs[PROPERTY_SUBDIVISIONS]

                return True
        return False

    def delete_component(self, component_id: str) -> bool:
        """Delete a component.

        Args:
            component_id (str): ID of the component to delete

        Returns:
            bool: True if deletion successful, False otherwise
        """
        for i, component in enumerate(self._components):
            if component.id == component_id:
                del self._components[i]
                if self._selected_component_id == component_id:
                    self._selected_component_id = None
                return True
        return False

    def select_component(self, component_id: Optional[str]):
        """Select a component.

        Args:
            component_id (Optional[str]): ID of component to select, or None to clear selection
        """
        self._selected_component_id = component_id

    def get_selected_component_id(self) -> Optional[str]:
        """Get the currently selected component ID.

        Returns:
            Optional[str]: Selected component ID or None
        """
        return self._selected_component_id

    def get_assets(self, asset_type: str = None) -> List[Dict[str, Any]]:
        """Get all assets, optionally filtered by type.

        Args:
            asset_type (str, optional): Filter by asset type

        Returns:
            List[Dict[str, Any]]: List of asset dictionaries (may be partial if loading is in progress)
        """
        # Return currently available assets (may be partial if still loading)
        assets = self._assets.copy()  # Thread-safe copy

        if asset_type:
            assets = [a for a in assets if a["asset_type"] == asset_type]

        # Log loading status for debugging
        if not self._loading_complete and assets:
            self._logger.debug(f"Returning {len(assets)} assets (loading still in progress)")
        elif self._loading_complete:
            self._logger.debug(f"Returning {len(assets)} assets (loading complete)")

        return assets

    def get_assets_sync(self, asset_type: str = None, timeout: float = 10.0) -> List[Dict[str, Any]]:
        """Get all assets synchronously, waiting for loading to complete if necessary.

        Args:
            asset_type (str, optional): Filter by asset type
            timeout (float): Maximum time to wait for loading completion

        Returns:
            List[Dict[str, Any]]: List of asset dictionaries
        """
        # Wait for loading to complete
        if not self._loading_complete:
            self._logger.info("Waiting for asset loading to complete...")
            self.wait_for_loading(timeout)

        return self.get_assets(asset_type)


# Backward compatibility alias
MockDataManager = DataManager
